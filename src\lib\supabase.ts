import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://tlehdbqdiqilpparmhkf.supabase.co';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Types for our reviews table
export interface Review {
  id: string;
  reviewer_name: string;
  reviewer_email?: string;
  rating: number;
  review_text: string;
  approved: boolean;
  created_at: string;
  updated_at: string;
}

export interface NewReview {
  reviewer_name: string;
  reviewer_email?: string;
  rating: number;
  review_text: string;
}

// API functions for reviews
export const reviewsApi = {
  // Submit a new review
  async submitReview(review: NewReview): Promise<{ data: Review | null; error: any }> {
    try {
      const { data, error } = await supabase
        .from('reviews')
        .insert([review])
        .select()
        .single();
      
      return { data, error };
    } catch (error) {
      return { data: null, error };
    }
  },

  // Get approved reviews with pagination
  async getApprovedReviews(page = 0, limit = 6): Promise<{ data: Review[] | null; error: any; count?: number }> {
    try {
      const { data, error, count } = await supabase
        .from('reviews')
        .select('*', { count: 'exact' })
        .eq('approved', true)
        .order('created_at', { ascending: false })
        .range(page * limit, (page + 1) * limit - 1);
      
      return { data, error, count: count || 0 };
    } catch (error) {
      return { data: null, error, count: 0 };
    }
  },

  // Get all reviews (for admin purposes)
  async getAllReviews(): Promise<{ data: Review[] | null; error: any }> {
    try {
      const { data, error } = await supabase
        .from('reviews')
        .select('*')
        .order('created_at', { ascending: false });
      
      return { data, error };
    } catch (error) {
      return { data: null, error };
    }
  }
};
