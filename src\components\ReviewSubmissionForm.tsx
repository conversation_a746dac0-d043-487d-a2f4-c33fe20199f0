'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { FaU<PERSON>, FaEnvelope, FaComment, FaPaperPlane } from 'react-icons/fa';
import StarRating from './StarRating';
import { reviewsApi, NewReview } from '@/lib/supabase';

interface ReviewSubmissionFormProps {
  onSubmitSuccess?: () => void;
}

const ReviewSubmissionForm = ({ onSubmitSuccess }: ReviewSubmissionFormProps) => {
  const [formData, setFormData] = useState<NewReview & { reviewer_email: string }>({
    reviewer_name: '',
    reviewer_email: '',
    rating: 0,
    review_text: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = useState('');

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleRatingChange = (rating: number) => {
    setFormData(prev => ({ ...prev, rating }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.reviewer_name.trim() || !formData.review_text.trim() || formData.rating === 0) {
      setErrorMessage('Please fill in all required fields and provide a rating.');
      setSubmitStatus('error');
      return;
    }

    setIsSubmitting(true);
    setSubmitStatus('idle');
    setErrorMessage('');

    try {
      const reviewData: NewReview = {
        reviewer_name: formData.reviewer_name.trim(),
        reviewer_email: formData.reviewer_email.trim() || undefined,
        rating: formData.rating,
        review_text: formData.review_text.trim()
      };

      const { data, error } = await reviewsApi.submitReview(reviewData);

      if (error) {
        throw new Error(error.message || 'Failed to submit review');
      }

      setSubmitStatus('success');
      setFormData({
        reviewer_name: '',
        reviewer_email: '',
        rating: 0,
        review_text: ''
      });
      
      if (onSubmitSuccess) {
        onSubmitSuccess();
      }
    } catch (error) {
      setSubmitStatus('error');
      setErrorMessage(error instanceof Error ? error.message : 'Failed to submit review. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (submitStatus === 'success') {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="bg-gray-800 rounded-lg p-8 text-center"
      >
        <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-r from-green-400 to-green-500 flex items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        </div>
        <h3 className="text-2xl font-bold mb-2 text-foreground">Thank You!</h3>
        <p className="text-foreground/70 mb-6">
          Your review has been submitted successfully. It will be published after approval.
        </p>
        <button
          onClick={() => setSubmitStatus('idle')}
          className="bg-gradient-to-r from-[var(--accent-primary)] to-[var(--accent-secondary)] text-white px-6 py-3 rounded-full hover:shadow-lg transition-all"
        >
          Submit Another Review
        </button>
      </motion.div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="bg-gray-800 rounded-lg p-6 md:p-8">
      <div className="space-y-6">
        {/* Name Field */}
        <div>
          <label htmlFor="reviewer_name" className="block text-gray-400 mb-2 flex items-center">
            <FaUser className="mr-2 text-[var(--accent-primary)]" />
            Name *
          </label>
          <input
            type="text"
            id="reviewer_name"
            name="reviewer_name"
            value={formData.reviewer_name}
            onChange={handleInputChange}
            required
            className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-[var(--accent-primary)] transition-all"
            placeholder="Your name"
          />
        </div>

        {/* Email Field */}
        <div>
          <label htmlFor="reviewer_email" className="block text-gray-400 mb-2 flex items-center">
            <FaEnvelope className="mr-2 text-[var(--accent-secondary)]" />
            Email (Optional)
          </label>
          <input
            type="email"
            id="reviewer_email"
            name="reviewer_email"
            value={formData.reviewer_email}
            onChange={handleInputChange}
            className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-[var(--accent-primary)] transition-all"
            placeholder="<EMAIL>"
          />
        </div>

        {/* Rating Field */}
        <div>
          <label className="block text-gray-400 mb-3">
            Rating *
          </label>
          <div className="flex items-center gap-4">
            <StarRating
              rating={formData.rating}
              onRatingChange={handleRatingChange}
              size="lg"
            />
            <span className="text-foreground/70 text-sm">
              {formData.rating > 0 ? `${formData.rating}/5` : 'Select a rating'}
            </span>
          </div>
        </div>

        {/* Review Text Field */}
        <div>
          <label htmlFor="review_text" className="block text-gray-400 mb-2 flex items-center">
            <FaComment className="mr-2 text-[var(--accent-tertiary)]" />
            Your Review *
          </label>
          <textarea
            id="review_text"
            name="review_text"
            value={formData.review_text}
            onChange={handleInputChange}
            required
            rows={4}
            className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-[var(--accent-primary)] transition-all resize-none"
            placeholder="Share your experience working with me..."
          />
        </div>

        {/* Error Message */}
        {submitStatus === 'error' && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-red-500/10 border border-red-500/20 rounded-lg p-4"
          >
            <p className="text-red-400 text-sm">{errorMessage}</p>
          </motion.div>
        )}

        {/* Submit Button */}
        <button
          type="submit"
          disabled={isSubmitting}
          className="w-full bg-gradient-to-r from-[var(--accent-primary)] to-[var(--accent-secondary)] text-white px-6 py-3 rounded-full hover:shadow-lg transition-all flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isSubmitting ? (
            <>
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Submitting...
            </>
          ) : (
            <>
              <FaPaperPlane className="mr-2" />
              Submit Review
            </>
          )}
        </button>
      </div>
    </form>
  );
};

export default ReviewSubmissionForm;
