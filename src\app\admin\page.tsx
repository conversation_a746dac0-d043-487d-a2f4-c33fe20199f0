'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FaCheck, FaTimes, FaEye, FaTrash, FaSpinner } from 'react-icons/fa';
import StarRating from '@/components/StarRating';
import { supabase, Review } from '@/lib/supabase';

const AdminPage = () => {
  const [reviews, setReviews] = useState<Review[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [processingIds, setProcessingIds] = useState<Set<string>>(new Set());

  const fetchAllReviews = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('reviews')
        .select('*')
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      setReviews(data || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch reviews');
    } finally {
      setLoading(false);
    }
  };

  const updateReviewStatus = async (id: string, approved: boolean) => {
    try {
      setProcessingIds(prev => new Set(prev).add(id));
      
      const { error } = await supabase
        .from('reviews')
        .update({ approved, updated_at: new Date().toISOString() })
        .eq('id', id);
      
      if (error) throw error;
      
      // Update local state
      setReviews(prev => prev.map(review => 
        review.id === id ? { ...review, approved } : review
      ));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update review');
    } finally {
      setProcessingIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(id);
        return newSet;
      });
    }
  };

  const deleteReview = async (id: string) => {
    if (!confirm('Are you sure you want to delete this review?')) return;
    
    try {
      setProcessingIds(prev => new Set(prev).add(id));
      
      const { error } = await supabase
        .from('reviews')
        .delete()
        .eq('id', id);
      
      if (error) throw error;
      
      setReviews(prev => prev.filter(review => review.id !== id));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete review');
    } finally {
      setProcessingIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(id);
        return newSet;
      });
    }
  };

  useEffect(() => {
    fetchAllReviews();
  }, []);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const pendingReviews = reviews.filter(r => !r.approved);
  const approvedReviews = reviews.filter(r => r.approved);

  if (loading) {
    return (
      <div className="min-h-screen bg-[var(--background)] flex items-center justify-center">
        <div className="text-center">
          <FaSpinner className="animate-spin text-4xl text-[var(--accent-primary)] mx-auto mb-4" />
          <p className="text-foreground">Loading reviews...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[var(--background)] py-8">
      <div className="container mx-auto px-4 max-w-6xl">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-foreground mb-2">Review Management</h1>
          <p className="text-foreground/70">Manage customer reviews and feedback</p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-foreground mb-2">Total Reviews</h3>
            <p className="text-3xl font-bold text-[var(--accent-primary)]">{reviews.length}</p>
          </div>
          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-foreground mb-2">Pending Approval</h3>
            <p className="text-3xl font-bold text-yellow-500">{pendingReviews.length}</p>
          </div>
          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-foreground mb-2">Approved</h3>
            <p className="text-3xl font-bold text-green-500">{approvedReviews.length}</p>
          </div>
        </div>

        {error && (
          <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4 mb-6">
            <p className="text-red-400">{error}</p>
            <button
              onClick={() => setError(null)}
              className="mt-2 text-red-400 hover:text-red-300 underline"
            >
              Dismiss
            </button>
          </div>
        )}

        {/* Pending Reviews */}
        {pendingReviews.length > 0 && (
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-foreground mb-4">Pending Reviews</h2>
            <div className="space-y-4">
              {pendingReviews.map((review) => (
                <ReviewCard
                  key={review.id}
                  review={review}
                  onApprove={() => updateReviewStatus(review.id, true)}
                  onReject={() => updateReviewStatus(review.id, false)}
                  onDelete={() => deleteReview(review.id)}
                  isProcessing={processingIds.has(review.id)}
                  isPending={true}
                />
              ))}
            </div>
          </div>
        )}

        {/* Approved Reviews */}
        {approvedReviews.length > 0 && (
          <div>
            <h2 className="text-2xl font-bold text-foreground mb-4">Approved Reviews</h2>
            <div className="space-y-4">
              {approvedReviews.map((review) => (
                <ReviewCard
                  key={review.id}
                  review={review}
                  onApprove={() => updateReviewStatus(review.id, true)}
                  onReject={() => updateReviewStatus(review.id, false)}
                  onDelete={() => deleteReview(review.id)}
                  isProcessing={processingIds.has(review.id)}
                  isPending={false}
                />
              ))}
            </div>
          </div>
        )}

        {reviews.length === 0 && (
          <div className="text-center py-12">
            <p className="text-foreground/70 text-lg">No reviews found.</p>
          </div>
        )}
      </div>
    </div>
  );
};

interface ReviewCardProps {
  review: Review;
  onApprove: () => void;
  onReject: () => void;
  onDelete: () => void;
  isProcessing: boolean;
  isPending: boolean;
}

const ReviewCard = ({ review, onApprove, onReject, onDelete, isProcessing, isPending }: ReviewCardProps) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`bg-gray-800 rounded-lg p-6 ${isPending ? 'border-l-4 border-yellow-500' : 'border-l-4 border-green-500'}`}
    >
      <div className="flex justify-between items-start mb-4">
        <div>
          <h3 className="text-lg font-semibold text-foreground">{review.reviewer_name}</h3>
          {review.reviewer_email && (
            <p className="text-foreground/70 text-sm">{review.reviewer_email}</p>
          )}
          <p className="text-foreground/50 text-xs">{formatDate(review.created_at)}</p>
        </div>
        <div className="flex items-center gap-4">
          <StarRating rating={review.rating} readonly size="sm" />
          <span className={`px-2 py-1 rounded text-xs font-medium ${
            review.approved ? 'bg-green-500/20 text-green-400' : 'bg-yellow-500/20 text-yellow-400'
          }`}>
            {review.approved ? 'Approved' : 'Pending'}
          </span>
        </div>
      </div>

      <p className="text-foreground/80 mb-4">"{review.review_text}"</p>

      <div className="flex gap-2">
        {isPending && (
          <button
            onClick={onApprove}
            disabled={isProcessing}
            className="flex items-center gap-2 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors disabled:opacity-50"
          >
            {isProcessing ? <FaSpinner className="animate-spin" /> : <FaCheck />}
            Approve
          </button>
        )}
        
        {!isPending && (
          <button
            onClick={onReject}
            disabled={isProcessing}
            className="flex items-center gap-2 bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg transition-colors disabled:opacity-50"
          >
            {isProcessing ? <FaSpinner className="animate-spin" /> : <FaEye />}
            Unapprove
          </button>
        )}
        
        <button
          onClick={onDelete}
          disabled={isProcessing}
          className="flex items-center gap-2 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors disabled:opacity-50"
        >
          {isProcessing ? <FaSpinner className="animate-spin" /> : <FaTrash />}
          Delete
        </button>
      </div>
    </motion.div>
  );
};

export default AdminPage;
