// <PERSON><PERSON><PERSON> to add sample reviews to the database
// Run with: node scripts/add-sample-reviews.js

const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://tlehdbqdiqilpparmhkf.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRsZWhkYnFkaXFpbHBwYXJtaGtmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM2MDk4ODksImV4cCI6MjA1OTE4NTg4OX0.11Xi0BmY1i9bOjPEDA01tNp410xHa5NnsWQk4fHcFMM';

const supabase = createClient(supabaseUrl, supabaseKey);

const sampleReviews = [
  {
    reviewer_name: '<PERSON>',
    reviewer_email: '<EMAIL>',
    rating: 5,
    review_text: 'Incredible work on our company website! The modern design and smooth animations really impressed our clients. Highly professional and delivered ahead of schedule.',
    approved: true
  },
  {
    reviewer_name: '<PERSON>',
    reviewer_email: '<EMAIL>',
    rating: 5,
    review_text: 'Working with this developer was a fantastic experience. They understood our vision perfectly and created a stunning portfolio site that showcases our work beautifully.',
    approved: true
  },
  {
    reviewer_name: '<PERSON>',
    reviewer_email: '<EMAIL>',
    rating: 4,
    review_text: 'Great attention to detail and excellent communication throughout the project. The e-commerce platform works flawlessly and our sales have increased significantly.',
    approved: true
  },
  {
    reviewer_name: 'Jennifer Lee',
    reviewer_email: '<EMAIL>',
    rating: 5,
    review_text: 'Amazing work on our nonprofit website! The donation system integration was seamless and the responsive design looks perfect on all devices.',
    approved: true
  },
  {
    reviewer_name: 'Carlos Rodriguez',
    reviewer_email: '<EMAIL>',
    rating: 4,
    review_text: 'Our restaurant website looks incredible! The online ordering system is intuitive and our customers love the new design. Thank you for the excellent work!',
    approved: true
  },
  {
    reviewer_name: 'Amanda Wilson',
    reviewer_email: '<EMAIL>',
    rating: 5,
    review_text: 'Professional, reliable, and incredibly talented. The business website exceeded our expectations and has helped us attract new clients consistently.',
    approved: true
  },
  {
    reviewer_name: 'Test User',
    reviewer_email: '<EMAIL>',
    rating: 3,
    review_text: 'This is a test review that should be pending approval. It demonstrates the moderation system working correctly.',
    approved: false
  },
  {
    reviewer_name: 'Another Test',
    reviewer_email: '<EMAIL>',
    rating: 4,
    review_text: 'Another pending review for testing the admin interface. This one has a 4-star rating.',
    approved: false
  }
];

async function addSampleReviews() {
  try {
    console.log('Adding sample reviews...');
    
    const { data, error } = await supabase
      .from('reviews')
      .insert(sampleReviews);
    
    if (error) {
      console.error('Error adding reviews:', error);
      return;
    }
    
    console.log('Successfully added sample reviews!');
    console.log(`Added ${sampleReviews.length} reviews to the database.`);
    
    // Get count of approved and pending reviews
    const { data: approved } = await supabase
      .from('reviews')
      .select('id')
      .eq('approved', true);
    
    const { data: pending } = await supabase
      .from('reviews')
      .select('id')
      .eq('approved', false);
    
    console.log(`Approved reviews: ${approved?.length || 0}`);
    console.log(`Pending reviews: ${pending?.length || 0}`);
    
  } catch (error) {
    console.error('Error:', error);
  }
}

addSampleReviews();
